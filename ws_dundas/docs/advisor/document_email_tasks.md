# 新增功能任务：文档中心与邮件发送

## 文档中心API (Document Center APIs)

**目标**: 为文档管理提供一套独立的RESTful API，实现前后端分离。

### 任务 1: 设计文档API
- [ ] `GET /api/v1/advisors/{advisorId}/documents`: 获取一个顾问的文档列表（元数据，不含文件内容）。
- [ ] `POST /api/v1/advisors/{advisorId}/documents`: 上传一个新文件。请求类型为multipart/form-data。
- [ ] `GET /api/v1/documents/{documentId}`: 下载一个文件的内容。
- [ ] `DELETE /api/v1/documents/{documentId}`: 删除一个文件。

### 任务 2: 实现文档服务 (DocumentService)
- [ ] **文件存储策略**: 确认文件存储策略。建议: 如果旧系统在数据库中存文件，新实现应迁移到文件系统或MinIO/S3。
- [ ] **核心逻辑**: 实现文件的上传、下载、删除逻辑，与文件存储系统交互。
- [ ] **元数据管理**: 使用JdbcTemplate操作STORED_FILE和关联表，管理文件元数据。

### 任务 3: 控制器与安全
- [ ] **控制器创建**: 创建 `AdvisorDocumentController`。
- [ ] **权限校验**: 为每个端点添加严格的权限校验（例如，`@permissionService.canAccessDocument(authentication, #documentId)`)。

## 邮件发送API (Action-Based APIs)

**目标**: 创建一个异步的、基于动作的API来处理邮件发送。

### 任务 1: 设计邮件发送API
- [ ] `POST /api/v1/advisors/{advisorId}/emails/send-licenses`: 触发发送执照/E&O信息的邮件。
- [ ] **请求体 DTO**: 创建 `SendEmailRequestDTO` (`to`: "<EMAIL>", `subject`: "...", `body`: "...")。

### 任务 2: 实现异步邮件服务 (AsyncEmailService)
- [ ] **异步方法**: 创建一个带`@Async`注解的服务方法 `sendLicenseInfoEmail(advisorId, requestDto)`。
- [ ] **数据加载**: 调用我们已有的 `AdvisorProfileRepository` 加载执照和责任险数据。
- [ ] **内容生成**: 使用模板引擎（如Thymeleaf）将数据渲染成HTML邮件正文。
- [ ] **邮件发送**: 调用旧的 `EmailManager` (通过SDK) 来发送邮件。这是最安全的方式，可以重用所有现有的SMTP配置和逻辑。
- [ ] **活动记录**: 调用旧的 `ActivityFacade` (通过SDK) 来记录活动日志。

### 任务 3: 控制器与配置
- [ ] **控制器实现**: 在 `AdvisorController` 或新的 `AdvisorActionController` 中创建该端点。
- [ ] **异步启用**: 在主应用类上启用异步处理 (`@EnableAsync`)。
- [ ] **API响应**: 端点应立即返回202 Accepted，表示任务已接受，正在后台处理。

## 相关EJB Facade及类分析 (初步)
根据项目文档和 `sky_ejb` 目录结构，以下 EJB 组件和类可能与这些新功能相关，值得在重构过程中进一步分析其具体方法和逻辑：

### 文档中心相关:
- `AFileFacade.java`: 可能包含通用的文件操作逻辑。
- `AdvisorFacade.java`: 可能包含将文档链接到顾问的逻辑。
- `ComplianceDocumentFacade.java`: 如果文档涉及合规性，此 Facade 可能相关。
- 旧系统可能通过特定的DAO或业务Bean直接处理与 `STORED_FILE` 表（或类似命名的表）相关的文件元数据和存储逻辑。

### 邮件发送相关:
- `EmailManager.java`: 核心的邮件发送逻辑 (已在计划中明确提出通过SDK调用)。
- `ActivityFacade.java`: 用于记录如“邮件已发送”之类的活动 (已在计划中明确提出通过SDK调用)。
- `AlertFacade.java`: 可能用于创建与邮件发送相关的提醒或通知。
- `EmailFacade.java`: 可能提供邮件相关的辅助功能或对 `EmailManager` 的封装。
- `DefaultEmailsFacade.java`: 可能管理邮件模板、默认收件人或特定场景的邮件配置。

对这些组件的深入分析将有助于确保在新系统中保留所有必要的业务规则，并顺利迁移或重用现有逻辑。
