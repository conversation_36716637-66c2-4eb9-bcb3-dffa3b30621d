###
# Advisor Profile API Tests
# 测试 Advisor Profile 重构项目的各个API端点
# 
# 使用说明:
# 1. 替换 {{base_url}} 为实际的服务器地址 (如: http://localhost:8080)
# 2. 替换 {{jwt_token}} 为有效的JWT令牌
# 3. 替换 {{session_id}} 为有效的会话ID (用于向后兼容测试)
# 4. 替换 {{advisor_id}} 和 {{user_id}} 为测试用的ID

# 设置变量
@base_url ={{$dotenv TEST_HOST}}
@jwt_token = {{$dotenv JWT_TOKEN}}
@session_id = {{$dotenv SESSION_ID}}
@advisor_id = {{$dotenv ADVISOR_ID}}
@user_id = {{$dotenv USER_ID}}       

###
# 0. 认证端点测试 - Session换取Token和JWT验证
# 这些端点用于获取和验证认证令牌

### 0.1 使用Session ID换取JWT Token
POST {{base_url}}/api/v1/auth/issue-token-from-session
Content-Type: application/json

{
  "sessionId": "{{session_id}}"
}

### 0.2 使用无效Session ID换取JWT Token (测试401错误)
POST {{base_url}}/api/v1/auth/issue-token-from-session
Content-Type: application/json

{
  "sessionId": "invalid_session_id_12345"
}

### 0.3 空Session ID换取JWT Token (测试400错误)
POST {{base_url}}/api/v1/auth/issue-token-from-session
Content-Type: application/json

{
  "sessionId": ""
}

### 0.4 缺少Session ID的请求 (测试400错误)
POST {{base_url}}/api/v1/auth/issue-token-from-session
Content-Type: application/json

{}

### 0.5 验证JWT Token (通过Authorization Header)
POST {{base_url}}/api/v1/auth/validate-jwt
Authorization: Bearer {{jwt_token}}
Content-Type: application/json

### 0.6 验证JWT Token (通过Request Body)
POST {{base_url}}/api/v1/auth/validate-jwt
Content-Type: application/json

{{jwt_token}}

### 0.7 验证无效JWT Token (测试401错误)
POST {{base_url}}/api/v1/auth/validate-jwt
Authorization: Bearer invalid_jwt_token_here
Content-Type: application/json

### 0.8 缺少JWT Token的验证请求 (测试400错误)
POST {{base_url}}/api/v1/auth/validate-jwt
Content-Type: application/json

### 0.9 同时提供Header和Body的JWT Token (Header优先)
POST {{base_url}}/api/v1/auth/validate-jwt
Authorization: Bearer {{jwt_token}}
Content-Type: application/json

backup_token_in_body

### 0.10 完整的认证流程测试 - Session转换为JWT并使用
# 步骤1: 使用Session获取JWT
POST {{base_url}}/api/v1/auth/issue-token-from-session
Content-Type: application/json

{
  "sessionId": "{{session_id}}"
}

# 步骤2: 使用获取的JWT访问受保护的资源
# 注意: 需要从上一步响应中复制JWT Token到下面的请求中
# GET {{base_url}}/api/v1/advisors/{{advisor_id}}
# Authorization: Bearer <从上一步获取的JWT>
# Accept: application/json

###
# 0.11 认证问题诊断测试 - JWT认证失败排查
# 如果遇到"anonymousUser"错误，按顺序执行以下测试

### 0.11.1 验证当前JWT令牌是否有效
POST {{base_url}}/api/v1/auth/validate-jwt
Authorization: Bearer {{jwt_token}}
Content-Type: application/json

### 0.11.2 如果JWT无效，使用Session换取新的JWT
POST {{base_url}}/api/v1/auth/issue-token-from-session
Content-Type: application/json

{
  "sessionId": "{{session_id}}"
}

### 0.11.3 测试JWT格式 - 检查是否为标准3段式JWT
POST {{base_url}}/api/v1/auth/validate-jwt
Content-Type: application/json

{{jwt_token}}


###
# 1. 主要API端点 - 获取顾问详细信息
# GET /api/v1/advisors/{advisorId}

### 1.1 使用JWT令牌获取顾问信息 (推荐方式)
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json

### 1.2 使用Session ID获取顾问信息 (向后兼容)
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{session_id}}
Accept: application/json

### 1.3 获取不存在的顾问 (测试404错误)
GET {{base_url}}/api/v1/advisors/99999
Authorization: Bearer {{jwt_token}}
Accept: application/json

### 1.4 无认证访问 (测试401错误)
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Accept: application/json

### 1.5 无效认证令牌 (测试401错误)
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer invalid_token_here
Accept: application/json

###
# 2. 辅助端点 - 检查顾问是否存在
# GET /api/v1/advisors/{advisorId}/exists

### 2.1 检查存在的顾问
GET {{base_url}}/api/v1/advisors/{{advisor_id}}/exists
Authorization: Bearer {{jwt_token}}
Accept: application/json

### 2.2 检查不存在的顾问
GET {{base_url}}/api/v1/advisors/99999/exists
Authorization: Bearer {{jwt_token}}
Accept: application/json

### 2.3 使用Session ID检查顾问存在性 (向后兼容)
GET {{base_url}}/api/v1/advisors/{{advisor_id}}/exists
Authorization: Bearer {{session_id}}
Accept: application/json

###
# 3. 调试端点 - 会话信息查询
# GET /api/debug/sessions/{userId}

### 3.1 查询用户的会话信息
GET {{base_url}}/api/debug/sessions/{{user_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json

### 3.2 查询不存在用户的会话信息
GET {{base_url}}/api/debug/sessions/99999
Authorization: Bearer {{jwt_token}}
Accept: application/json

### 3.3 查询所有活跃会话 (如果支持)
GET {{base_url}}/api/debug/sessions
Authorization: Bearer {{jwt_token}}
Accept: application/json

###
# 4. 健康检查和系统信息

### 4.1 应用健康检查
GET {{base_url}}/actuator/health
Accept: application/json

### 4.2 API信息
GET {{base_url}}/actuator/info
Accept: application/json

###
# 5. OpenAPI文档访问

### 5.1 Swagger UI界面
GET {{base_url}}/swagger-ui/index.html
Accept: text/html

### 5.2 OpenAPI JSON规范
GET {{base_url}}/v3/api-docs
Accept: application/json

### 5.3 OpenAPI YAML规范
GET {{base_url}}/v3/api-docs.yaml
Accept: application/yaml

###
# 6. 错误场景测试

### 6.1 无效的顾问ID格式 (测试400错误)
GET {{base_url}}/api/v1/advisors/invalid_id
Authorization: Bearer {{jwt_token}}
Accept: application/json

### 6.2 负数顾问ID (测试400错误)
GET {{base_url}}/api/v1/advisors/-1
Authorization: Bearer {{jwt_token}}
Accept: application/json

### 6.3 超长顾问ID (测试400错误)
GET {{base_url}}/api/v1/advisors/999999999999999999999
Authorization: Bearer {{jwt_token}}
Accept: application/json

###
# 7. 权限测试场景

### 7.1 Admin用户访问任意顾问信息
GET {{base_url}}/api/v1/advisors/12345
Authorization: Bearer {{jwt_token}}
Accept: application/json
# 注意: 需要使用具有Admin权限的JWT令牌

### 7.2 AGA用户访问下属顾问信息
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json
# 注意: 需要使用具有AGA权限的JWT令牌

### 7.3 普通顾问用户只能访问自己的信息
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json
# 注意: JWT令牌中的用户ID应该与advisor_id一致

### 7.4 普通顾问用户尝试访问其他顾问信息 (测试403错误)
GET {{base_url}}/api/v1/advisors/12345
Authorization: Bearer {{jwt_token}}
Accept: application/json
# 注意: JWT令牌中的用户ID与advisor_id不一致

###
# 8. 性能和负载测试示例

### 8.1 批量请求测试 (连续多次请求)
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json

###
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json

###
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json

###
# 9. 内容协商测试

### 9.1 请求XML格式 (如果支持)
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/xml

### 9.2 请求纯文本格式 (测试406错误)
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: text/plain

###
# 10. 缓存测试

### 10.1 首次请求 (生成缓存)
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json
Cache-Control: no-cache

### 10.2 缓存请求 (使用缓存)
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json

### 10.3 强制刷新缓存
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json
Cache-Control: no-cache, no-store, must-revalidate

###
# 11. Phase 2 Task 2.1 - Company Information Tab 测试
# 测试顾问公司信息的获取和显示功能
#
# 实施状态: ✅ 已完成 - 重构版本 (2025-01-01)
# 功能描述: 扩展 GET /api/v1/advisors/{advisorId} 端点以返回公司信息
# 重构说明: 使用现代化 JDBC Template 替代有问题的 EJB CompanyFacade
# 预期响应: AdvisorProfileDTO 中包含 companies 字段 (List<CompanyDTO>)

### 11.1 获取包含公司信息的顾问详细信息 - 基础测试
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json

# 预期响应结构:
# {
#   "advisorId": 12345,
#   "firstName": "John",
#   "lastName": "Doe",
#   "companies": [
#     {
#       "companyId": 67890,
#       "companyIntId": 456,
#       "nameEn": "ABC Financial Services Inc.",
#       "nameFr": "Services Financiers ABC Inc.",
#       "primaryName": "ABC Financial",
#       "otherName": "ABC Corp",
#       "provincialBusinessNumber": "123456789BC0001",
#       "companyType": 1,
#       "businessStartDate": "2020-01-15",
#       "active": "Y",
#       "assignableCommissions": "Y",
#       "buildingType": 2,
#       "creationDate": "2020-01-15T10:30:00",
#       "lastModificationDate": "2024-12-01T15:45:00",
#       "isPrimary": true,
#       "businessAddress": {
#         "addressId": 101,
#         "addressLine1": "123 Business Street",
#         "addressLine2": "Suite 400",
#         "city": "Vancouver",
#         "province": "BC",
#         "postalCode": "V6B 1A1",
#         "country": "Canada"
#       }
#     }
#   ]
# }

### 11.2 验证公司信息字段完整性 - 详细检查
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json

# 验证要点:
# ✅ companies 字段存在且为数组
# ✅ 每个公司对象包含所有必需字段
# ✅ companyId 和 companyIntId 为数字类型
# ✅ 日期字段格式正确 (ISO 8601)
# ✅ active 和 assignableCommissions 为 Y/N 字符
# ✅ isPrimary 为布尔值
# ✅ businessAddress 嵌套对象结构正确

### 11.3 测试无公司信息的顾问 - 空列表处理
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json

# 预期结果:
# - companies 字段应为空数组 []，而不是 null
# - 其他顾问信息正常返回
# - HTTP 状态码仍为 200 OK

### 11.4 测试多公司顾问 - 数组处理
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json

# 验证要点:
# ✅ companies 数组包含多个公司对象
# ✅ 每个公司对象结构一致
# ✅ isPrimary 字段正确标识主要公司
# ✅ 公司排序逻辑 (如果有的话)

### 11.5 公司地址信息验证 - 嵌套对象测试
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json

# 重点验证 businessAddress 字段:
# ✅ 地址对象完整性
# ✅ 省份和国家信息正确
# ✅ 邮政编码格式
# ✅ 地址行信息完整

### 11.6 公司类型和状态验证 - 业务逻辑测试
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json

# 验证业务规则:
# ✅ companyType 值在有效范围内
# ✅ active 状态为 Y 或 N
# ✅ assignableCommissions 逻辑正确
# ✅ buildingType 值合理

### 11.7 公司信息性能测试 - 响应时间
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json

# 性能指标:
# ⏱️ 响应时间应 < 2秒
# 📊 单次查询获取所有公司信息 (避免 N+1 问题)
# 💾 内存使用合理

### 11.8 公司信息错误处理 - 异常场景
GET {{base_url}}/api/v1/advisors/99999
Authorization: Bearer {{jwt_token}}
Accept: application/json

# 错误场景验证:
# ❌ 不存在的顾问 ID → 404 Not Found
# ❌ companies 字段在错误响应中不应出现
# ✅ 错误消息清晰明确

###
# 11.9 Task 2.1 实施验证清单
#
# ✅ CompanyDTO 创建完成
#    - 包含所有必需字段 (companyId, nameEn, nameFr, etc.)
#    - 支持嵌套 AddressDTO
#    - 完整的 Swagger 注解
#
# ✅ AdvisorProfileDTO 扩展完成
#    - 添加 List<CompanyDTO> companies 字段
#    - Builder 模式支持
#    - 向后兼容性保持
#
# ✅ AdvisorService 重构完成
#    - 使用现代化 CompanyRepository 替代 EJB Facade
#    - enrichAdvisorProfileWithCompanies 方法
#    - 错误处理和日志记录
#
# ✅ AdvisorMapper 扩展完成
#    - toCompanyDTO 方法实现
#    - 防御性编程 (try-catch)
#    - 实体字段映射完整
#
# ✅ CompanyRepository 重构完成 (关键改进)
#    - 修复了 CompanyFacade.companiesByContact 的参数名 BUG
#    - 基于真实 SKYTEST 数据库 schema 编写正确的 SQL 查询
#    - 使用正确的表关系: COMPANY.CONTACT = CONTACT.CONTACT_INT_ID
#    - 使用正确的字段名: NAME_EN, NAME_FR, FIRSTNAME, LASTNAME 等
#    - 统一使用 JDBC Template，避免 JPA/JDBC 混合问题
#    - 单查询方式避免 N+1 问题
#    - 正确的资源管理和错误处理
#    - 创建了专门的数据库 schema 文档用于后续维护
#
# ✅ 测试覆盖
#    - AdvisorMapperTest 通过 (4/4)
#    - 编译测试通过
#    - API 测试脚本就绪

###
# 测试完成
#
# 预期结果说明:
# - 200 OK: 成功获取顾问信息 (包含公司信息)
# - 401 Unauthorized: 未提供或无效的认证令牌
# - 403 Forbidden: 没有权限访问该顾问信息
# - 404 Not Found: 顾问不存在
# - 400 Bad Request: 无效的请求参数
# - 406 Not Acceptable: 不支持的内容类型
# - 500 Internal Server Error: 服务器内部错误
#
# Task 2.1 状态: ✅ 重构完成 - 基于真实 Schema 的现代化实现
# 重构亮点:
#   - 解决了原 CompanyFacade 的参数名 BUG (:contactId vs "contactIntId")
#   - 基于真实 SKYTEST 数据库 schema 编写正确的 SQL 查询
#   - 正确的表关系映射: COMPANY.CONTACT = CONTACT.CONTACT_INT_ID
#   - 统一数据访问层架构 (纯 JDBC Template)
#   - 创建了数据库 schema 文档 (/docs/advisor/database_schema_for_refactoring.md)
#   - 提升了性能和可维护性
# 下一步: Task 2.2 - License Information Tab

###
# 12. Phase 2 Task 2.2 - License Information Tab 测试
# 测试顾问许可证信息的获取和显示功能
#
# 实施状态: ✅ 已完成 - 重构版本 (2025-01-01)
# 功能描述: 扩展 GET /api/v1/advisors/{advisorId} 端点以返回许可证信息
# 重构说明: 使用现代化 JDBC Template 替代 EJB LicenseFacade
# 预期响应: AdvisorProfileDTO 中包含 licenses 字段 (List<LicenseDTO>)

### 12.1 获取包含许可证信息的顾问详细信息 - 基础测试
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json

# 预期响应结构:
# {
#   "advisorId": 12345,
#   "firstName": "John",
#   "lastName": "Doe",
#   "licenses": [
#     {
#       "licenseId": 789,
#       "licenseNumber": "L123456789",
#       "province": "BC",
#       "startDate": "2020-01-01",
#       "endDate": "2025-12-31",
#       "licenseDescription": "Life Insurance",
#       "clientNumber": *********,
#       "status": 1,
#       "selected": true,
#       "creationDate": "2020-01-01T09:00:00",
#       "lastModificationDate": "2024-12-01T14:30:00",
#       "isActive": true,
#       "company": {
#         "companyId": 456,
#         "primaryName": "Insurance Company Ltd."
#       },
#       "agency": {
#         "agencyId": 123,
#         "name": "Regional Agency"
#       },
#       "licenseLiability": {
#         "liabilityId": 321,
#         "liabilityNumber": "LIA789456",
#         "startDate": "2020-01-01",
#         "endDate": "2025-12-31",
#         "insuranceCompany": "Liability Insurance Corp",
#         "status": 1
#       }
#     }
#   ]
# }

### 12.2 验证许可证信息字段完整性 - 详细检查
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json

# 验证要点:
# ✅ licenses 字段存在且为数组
# ✅ 每个许可证对象包含所有必需字段
# ✅ licenseId 和 clientNumber 为数字类型
# ✅ 日期字段格式正确 (ISO 8601)
# ✅ isActive 为布尔值
# ✅ company, agency, licenseLiability 嵌套对象结构正确

### 12.3 测试无许可证的顾问 - 空列表处理
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json

# 预期结果:
# - licenses 字段应为空数组 []，而不是 null
# - 其他顾问信息正常返回
# - HTTP 状态码仍为 200 OK

###
# 13. Phase 2 Task 2.3 - Contract Information Tab 测试
# 测试顾问合同信息的获取和显示功能
#
# 实施状态: ✅ 已完成 - 重构版本 (2025-01-01)
# 功能描述: 扩展 GET /api/v1/advisors/{advisorId} 端点以返回合同信息
# 重构说明: 使用现代化 JDBC Template 替代 EJB ContractFacade
# 预期响应: AdvisorProfileDTO 中包含 contracts 字段 (List<ContractDTO>)

### 13.1 获取包含合同信息的顾问详细信息 - 基础测试
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json

# 预期响应结构:
# {
#   "advisorId": 12345,
#   "firstName": "John",
#   "lastName": "Doe",
#   "contracts": [
#     {
#       "contractId": 567,
#       "contractType": 1,
#       "contractNumber": "CON-2024-001",
#       "contractStatus": 1,
#       "effectiveDate": "2024-01-01",
#       "inactiveDate": null,
#       "salesrepCode": "SR001",
#       "commissionPayTo": "John Doe",
#       "mfCode": "MF001",
#       "dealerCode": "D001",
#       "district": "WEST",
#       "isPrimary": true,
#       "directDeposit": true,
#       "eftFormIncluded": true,
#       "eftSentDate": "2024-01-15",
#       "healthCode": "HC001",
#       "transferInPrice": 1000.00,
#       "transferOutPrice": 950.00,
#       "transferInDate": "2024-02-01",
#       "transferOutDate": null,
#       "companyAssociation": 123,
#       "transferPaidDate": "2024-02-20",
#       "payScale": "A",
#       "isActive": true,
#       "contractSetup": {
#         "contractSetupId": 890,
#         "productSupplierId": 100,
#         "productSupplierName": "ABC Insurance",
#         "agencyId": 200,
#         "agencyName": "XYZ Agency",
#         "companyId": 300,
#         "companyName": "DEF Company",
#         "servicingAgencyId": 250,
#         "servicingAgencyName": "Service Agency",
#         "agaAdvisorId": 400,
#         "agaAdvisorName": "John Smith",
#         "mgaProductSupplierId": 150,
#         "mgaProductSupplierName": "MGA Insurance",
#         "agaProductSupplierId": 160,
#         "agaProductSupplierName": "AGA Insurance",
#         "aga2ProductSupplierId": 170,
#         "aga2ProductSupplierName": "AGA2 Insurance"
#       },
#       "eftInfo": {
#         "eftId": 456,
#         "bankHolder": "John Doe",
#         "transit": "12345",
#         "accountNumber": "****567890",
#         "bank": "001"
#       }
#     }
#   ]
# }

### 13.2 验证合同信息字段完整性 - 详细检查
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json

# 验证要点:
# ✅ contracts 字段存在且为数组
# ✅ 每个合同对象包含所有必需字段
# ✅ contractId 和相关ID字段为数字类型
# ✅ 日期字段格式正确 (ISO 8601)
# ✅ 布尔字段 (isPrimary, directDeposit, eftFormIncluded, isActive) 正确
# ✅ 金额字段 (transferInPrice, transferOutPrice) 为数字类型
# ✅ contractSetup 嵌套对象结构正确
# ✅ eftInfo 嵌套对象结构正确

### 13.3 测试无合同的顾问 - 空列表处理
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json

# 预期结果:
# - contracts 字段应为空数组 []，而不是 null
# - 其他顾问信息正常返回
# - HTTP 状态码仍为 200 OK

### 13.4 测试多合同顾问 - 数组处理和排序
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json

# 验证要点:
# ✅ contracts 数组包含多个合同对象
# ✅ 每个合同对象结构一致
# ✅ 合同排序逻辑：活跃合同优先，按生效日期排序
# ✅ isPrimary 字段正确标识主要合同

### 13.5 合同设置信息验证 - 嵌套对象测试
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json

# 重点验证 contractSetup 字段:
# ✅ 产品供应商信息完整 (productSupplier, mgaProductSupplier, agaProductSupplier, aga2ProductSupplier)
# ✅ 代理机构信息正确 (agency, servicingAgency)
# ✅ 公司信息完整
# ✅ AGA顾问信息正确

###
# 14. Phase 2 Task 2.4 - EFT Information Tab 测试
# 测试顾问EFT电子转账信息的获取和显示功能
#
# 实施状态: ✅ 已完成 - 重构版本 (2025-01-01)
# 功能描述: 扩展 GET /api/v1/advisors/{advisorId} 端点以返回EFT信息
# 重构说明: 使用现代化 JDBC Template 替代 EJB ContractEftFacade
# 预期响应: AdvisorProfileDTO 中包含 efts 字段 (List<EftDTO>)

### 14.1 获取包含EFT信息的顾问详细信息 - 基础测试
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json

# 预期响应结构:
# {
#   "advisorId": 12345,
#   "firstName": "John",
#   "lastName": "Doe",
#   "efts": [
#     {
#       "eftId": 789,
#       "sentDate": "2024-01-15",
#       "bankHolder": "John Doe",
#       "bankAddress": "123 Bank Street, Toronto, ON",
#       "transit": "12345",
#       "accountNumber": "****567890",
#       "copoEft": "Y",
#       "statementsByEmail": "Y",
#       "bank": "001",
#       "eftInfoId": 98765,
#       "advisorId": 12345,
#       "companyId": 55555,
#       "agencyId": 66666,
#       "deleted": "N",
#       "deletedDate": null,
#       "fileName": "eft_form_12345.pdf",
#       "isActive": true,
#       "isCopoEftEnabled": true,
#       "isStatementsByEmailEnabled": true,
#       "maskedAccountNumber": "****567890",
#       "displayName": "John Doe (****567890)"
#     }
#   ]
# }

### 14.2 验证EFT信息字段完整性 - 详细检查
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json

# 验证要点:
# ✅ efts 字段存在且为数组
# ✅ 每个EFT对象包含所有必需字段
# ✅ eftId 和相关ID字段为数字类型
# ✅ 日期字段格式正确 (ISO 8601)
# ✅ 布尔字段 (isActive, isCopoEftEnabled, isStatementsByEmailEnabled) 正确
# ✅ 银行账号已正确掩码处理 (maskedAccountNumber)
# ✅ 显示名称格式正确 (displayName)

### 14.3 测试无EFT信息的顾问 - 空列表处理
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json

# 预期结果:
# - efts 字段应为空数组 []，而不是 null
# - 其他顾问信息正常返回
# - HTTP 状态码仍为 200 OK

### 14.4 测试多EFT记录顾问 - 数组处理和排序
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json

# 验证要点:
# ✅ efts 数组包含多个EFT对象
# ✅ 每个EFT对象结构一致
# ✅ EFT排序逻辑：活跃EFT优先，按发送日期排序
# ✅ 已删除的EFT记录不显示 (deleted != 'Y')

### 14.5 EFT安全性验证 - 敏感信息处理
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json

# 重点验证安全性:
# ✅ 银行账号已掩码处理，只显示后4位
# ✅ 敏感信息不在日志中暴露
# ✅ 完整账号信息不在API响应中返回
# ✅ maskedAccountNumber 字段正确实现

### 14.6 EFT状态和标志验证 - 业务逻辑测试
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json

# 验证业务规则:
# ✅ isActive 状态正确 (deleted != 'Y')
# ✅ isCopoEftEnabled 逻辑正确 (copoEft == 'Y')
# ✅ isStatementsByEmailEnabled 逻辑正确 (statementsByEmail == 'Y')
# ✅ displayName 格式化正确

###
# 15. 综合功能测试 - 完整的顾问档案
# 测试包含所有信息的完整顾问档案获取

### 15.1 获取完整顾问档案 - 包含所有Tab信息
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json

# 预期响应包含所有已实现的Tab信息:
# ✅ 个人信息 (Personal Information) - Phase 1
# ✅ 公司信息 (Company Information) - Task 2.1
# ✅ 许可证信息 (License Information) - Task 2.2
# ✅ 合同信息 (Contract Information) - Task 2.3
# ✅ EFT信息 (EFT Information) - Task 2.4

### 15.2 验证完整响应结构 - 所有字段存在
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json

# 验证完整性:
# ✅ advisorId, advisorCode, status, advisorType 等基本字段
# ✅ contact 对象包含个人联系信息
# ✅ companies 数组包含公司信息
# ✅ licenses 数组包含许可证信息
# ✅ contracts 数组包含合同信息
# ✅ efts 数组包含EFT信息

### 15.3 性能测试 - 完整档案响应时间
GET {{base_url}}/api/v1/advisors/{{advisor_id}}
Authorization: Bearer {{jwt_token}}
Accept: application/json

# 性能指标:
# ⏱️ 响应时间应 < 3秒 (包含所有Tab信息)
# 📊 使用优化的SQL查询避免N+1问题
# 💾 内存使用合理
# 🔄 单次API调用获取所有信息

###
# 16. Phase 2 实施验证清单 - Tasks 2.3 & 2.4
#
# ✅ Task 2.3 - Contract Information Tab 完成
#    - ContractDTO 和 ContractSetupDTO 创建完成
#    - AdvisorProfileDTO 扩展 contracts 字段
#    - ContractRepository 实现 (复杂SQL查询)
#    - AdvisorService 集成 enrichAdvisorProfileWithContracts
#    - AdvisorMapper 实现 toContractDTO 和 toContractSetupDTO
#    - 防御性编程和错误处理
#    - 智能排序：活跃合同优先，按生效日期排序
#
# ✅ Task 2.4 - EFT Information Tab 完成
#    - EftDTO 创建完成 (包含安全掩码功能)
#    - AdvisorProfileDTO 扩展 efts 字段
#    - EftRepository 实现 (支持直接和关联查询)
#    - AdvisorService 集成 enrichAdvisorProfileWithEfts
#    - AdvisorMapper 实现 toEftDTO
#    - 银行账号安全掩码处理
#    - 智能排序：活跃EFT优先，按发送日期排序
#
# ✅ 数据库Schema分析完成
#    - 更新 database_schema_for_refactoring.md (Version 5.0)
#    - 添加 CONTRACT_EFT 和 CONTRACT 表完整结构
#    - 记录表关系和字段映射
#
# ✅ 技术实现亮点
#    - 使用复杂SQL JOIN查询避免N+1问题
#    - 实现防御性编程处理entity方法可能不存在的情况
#    - 正确处理 CONTRACT → CONTRACT_SETUP → PRODUCT_SUPPLIER/AGENCY/COMPANY 关系
#    - 银行账号安全掩码处理 (只显示后4位)
#    - 完整的错误处理和日志记录
#    - 遵循所有工作流程规范

###
# 测试完成状态
#
# Phase 2 当前进度:
# ✅ Task 2.1 - Company Information Tab (已完成)
# ✅ Task 2.2 - License Information Tab (已完成)
# ✅ Task 2.3 - Contract Information Tab (已完成)
# ✅ Task 2.4 - EFT Information Tab (已完成)
#
# 下一步: Phase 3 - 写入操作实现