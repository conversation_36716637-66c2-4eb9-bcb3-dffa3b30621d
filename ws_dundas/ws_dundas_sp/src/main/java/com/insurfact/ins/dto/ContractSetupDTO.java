package com.insurfact.ins.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * DTO for Contract Setup information in Advisor Profile
 * 
 * <AUTHOR> zhu
 * @since 2025-01-01
 */
@Schema(description = "Contract setup information for an advisor")
public class ContractSetupDTO {

    @Schema(description = "Contract setup internal ID", example = "12345")
    @JsonProperty("contractSetupId")
    private Long contractSetupId;

    @Schema(description = "Product supplier ID", example = "100")
    @JsonProperty("productSupplierId")
    private Long productSupplierId;

    @Schema(description = "Product supplier name", example = "ABC Insurance")
    @JsonProperty("productSupplierName")
    private String productSupplierName;

    @Schema(description = "Agency ID", example = "200")
    @JsonProperty("agencyId")
    private Long agencyId;

    @Schema(description = "Agency name", example = "XYZ Agency")
    @JsonProperty("agencyName")
    private String agencyName;

    @Schema(description = "Company ID", example = "300")
    @JsonProperty("companyId")
    private Long companyId;

    @Schema(description = "Company name", example = "DEF Company")
    @JsonProperty("companyName")
    private String companyName;

    @Schema(description = "Servicing agency ID", example = "250")
    @JsonProperty("servicingAgencyId")
    private Long servicingAgencyId;

    @Schema(description = "Servicing agency name", example = "Service Agency")
    @JsonProperty("servicingAgencyName")
    private String servicingAgencyName;

    @Schema(description = "AGA advisor ID", example = "400")
    @JsonProperty("agaAdvisorId")
    private Long agaAdvisorId;

    @Schema(description = "AGA advisor name", example = "John Smith")
    @JsonProperty("agaAdvisorName")
    private String agaAdvisorName;

    @Schema(description = "MGA product supplier ID", example = "150")
    @JsonProperty("mgaProductSupplierId")
    private Long mgaProductSupplierId;

    @Schema(description = "MGA product supplier name", example = "MGA Insurance")
    @JsonProperty("mgaProductSupplierName")
    private String mgaProductSupplierName;

    @Schema(description = "AGA product supplier ID", example = "160")
    @JsonProperty("agaProductSupplierId")
    private Long agaProductSupplierId;

    @Schema(description = "AGA product supplier name", example = "AGA Insurance")
    @JsonProperty("agaProductSupplierName")
    private String agaProductSupplierName;

    @Schema(description = "AGA2 product supplier ID", example = "170")
    @JsonProperty("aga2ProductSupplierId")
    private Long aga2ProductSupplierId;

    @Schema(description = "AGA2 product supplier name", example = "AGA2 Insurance")
    @JsonProperty("aga2ProductSupplierName")
    private String aga2ProductSupplierName;

    // Default constructor
    public ContractSetupDTO() {}

    // Getters and Setters
    public Long getContractSetupId() { return contractSetupId; }
    public void setContractSetupId(Long contractSetupId) { this.contractSetupId = contractSetupId; }

    public Long getProductSupplierId() { return productSupplierId; }
    public void setProductSupplierId(Long productSupplierId) { this.productSupplierId = productSupplierId; }

    public String getProductSupplierName() { return productSupplierName; }
    public void setProductSupplierName(String productSupplierName) { this.productSupplierName = productSupplierName; }

    public Long getAgencyId() { return agencyId; }
    public void setAgencyId(Long agencyId) { this.agencyId = agencyId; }

    public String getAgencyName() { return agencyName; }
    public void setAgencyName(String agencyName) { this.agencyName = agencyName; }

    public Long getCompanyId() { return companyId; }
    public void setCompanyId(Long companyId) { this.companyId = companyId; }

    public String getCompanyName() { return companyName; }
    public void setCompanyName(String companyName) { this.companyName = companyName; }

    public Long getServicingAgencyId() { return servicingAgencyId; }
    public void setServicingAgencyId(Long servicingAgencyId) { this.servicingAgencyId = servicingAgencyId; }

    public String getServicingAgencyName() { return servicingAgencyName; }
    public void setServicingAgencyName(String servicingAgencyName) { this.servicingAgencyName = servicingAgencyName; }

    public Long getAgaAdvisorId() { return agaAdvisorId; }
    public void setAgaAdvisorId(Long agaAdvisorId) { this.agaAdvisorId = agaAdvisorId; }

    public String getAgaAdvisorName() { return agaAdvisorName; }
    public void setAgaAdvisorName(String agaAdvisorName) { this.agaAdvisorName = agaAdvisorName; }

    public Long getMgaProductSupplierId() { return mgaProductSupplierId; }
    public void setMgaProductSupplierId(Long mgaProductSupplierId) { this.mgaProductSupplierId = mgaProductSupplierId; }

    public String getMgaProductSupplierName() { return mgaProductSupplierName; }
    public void setMgaProductSupplierName(String mgaProductSupplierName) { this.mgaProductSupplierName = mgaProductSupplierName; }

    public Long getAgaProductSupplierId() { return agaProductSupplierId; }
    public void setAgaProductSupplierId(Long agaProductSupplierId) { this.agaProductSupplierId = agaProductSupplierId; }

    public String getAgaProductSupplierName() { return agaProductSupplierName; }
    public void setAgaProductSupplierName(String agaProductSupplierName) { this.agaProductSupplierName = agaProductSupplierName; }

    public Long getAga2ProductSupplierId() { return aga2ProductSupplierId; }
    public void setAga2ProductSupplierId(Long aga2ProductSupplierId) { this.aga2ProductSupplierId = aga2ProductSupplierId; }

    public String getAga2ProductSupplierName() { return aga2ProductSupplierName; }
    public void setAga2ProductSupplierName(String aga2ProductSupplierName) { this.aga2ProductSupplierName = aga2ProductSupplierName; }

    /**
     * Convenience method to get primary product supplier display name
     */
    public String getPrimaryProductSupplierDisplay() {
        if (productSupplierName != null && !productSupplierName.trim().isEmpty()) {
            return productSupplierName;
        }
        return "Product Supplier #" + (productSupplierId != null ? productSupplierId : "Unknown");
    }

    /**
     * Convenience method to get agency display name
     */
    public String getAgencyDisplay() {
        if (agencyName != null && !agencyName.trim().isEmpty()) {
            return agencyName;
        }
        return "Agency #" + (agencyId != null ? agencyId : "Unknown");
    }
}
